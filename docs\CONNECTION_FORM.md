# Connection Form Component

## Overview

The Connection Form component allows users to dynamically connect to the PocketOption broker using their SSID (Session ID) and UID (User ID) credentials. This replaces the previous system that relied on environment variables for connection credentials.

## Features

### 1. **Dynamic Connection Management**

- Input fields for SSID and UID credentials
- Real-time connection status display
- Connect/Disconnect functionality
- Automatic credential validation

### 2. **Visual Feedback**

- Connection status indicator with color-coded states:
  - 🔴 Red: Disconnected
  - 🟡 Yellow: Connecting
  - 🔵 Blue: Connected
  - 🟢 Green: Authenticated
- Success/error message display
- Loading states during connection attempts

### 3. **User Experience**

- Form fields are disabled when connected to prevent accidental changes
- Automatic form validation (SSID required, UID must be numeric)
- Credential persistence using localStorage
- Connected account information display

### 4. **Integration**

- Positioned above the TradeForm in the right sidebar
- Consistent styling with existing components
- Responsive design that fits the current layout

## Usage

### Getting Your Credentials

1. **SSID (Session ID)**: This is your PocketOption session identifier
   - Obtained from your browser's developer tools or PocketOption API
   - Format: Usually a long string starting with specific characters

2. **UID (User ID)**: Your numeric user identifier
   - Must be a valid number
   - Obtained from your PocketOption account

### Using the Form

1. **First Time Setup**:
   - Enter your SSID in the "SSID (Session ID)" field
   - Enter your UID in the "UID (User ID)" field
   - Click "Connect" to establish connection

2. **Subsequent Uses**:
   - Credentials are automatically saved and restored
   - Connection status is displayed at the top
   - Use "Disconnect" to terminate connection

3. **Connection States**:
   - **Disconnected**: Ready to connect with new credentials
   - **Connecting**: Connection attempt in progress
   - **Connected**: Successfully connected to broker
   - **Authenticated**: Fully authenticated and ready for trading

## Technical Implementation

### Component Structure

```
ConnectionForm/
├── Form inputs (SSID, UID)
├── Connection status display
├── Error/success messaging
├── Connect/Disconnect button
└── Connected account info (when connected)
```

### API Integration

- `broker:connect` - Initiates connection with provided credentials
- `broker:disconnect` - Terminates current connection
- `broker:getState` - Retrieves current connection state
- Real-time status updates via `broker:event` channel

### State Management

- Local component state for form data and UI states
- Connection status managed via `useConnectionStatus` hook
- Credential persistence using browser localStorage

## Error Handling

The form includes comprehensive error handling for:

- Empty or invalid credentials
- Connection failures
- Network issues
- Authentication errors

Error messages are displayed in a red-bordered container below the form fields and automatically clear after 5 seconds.

## Security Considerations

- Credentials are stored in localStorage (consider encryption for production)
- SSID is partially masked in the connected account display
- Form fields are disabled when connected to prevent credential tampering

## Future Enhancements

Potential improvements for the connection form:

1. Credential encryption in localStorage
2. Multiple account management
3. Connection health monitoring
4. Automatic reconnection on failure
5. Import/export of connection profiles
